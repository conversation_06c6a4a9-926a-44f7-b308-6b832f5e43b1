#include <Windows.h>
#include <stdio.h>
#include <string.h>
#include <job.h>
#include <scanner.h>

DWORD WINAPI jobThread(LPVOID lpParam)
{
    AppData* data = (AppData*) lpParam;
    memset(buffer, 0, BUFLEN);

    if (!scanner_loadDLL(data->settings->scannerCom+1)) {
        printf("ERROR: Failed to open Scanner\n");
        return 0;
    }

    scanner_versionDll(buffer);
    printf("INFO: Scanner Version %s\n", buffer);

    memset(buffer, 0, BUFLEN);
    scanner_engineVersion(buffer, 0);
    printf("INFO: Engine Version (ENG00051) %s\n", buffer);

    memset(buffer, 0, BUFLEN);
    scanner_engineVersion(buffer, 1);
    printf("INFO: Engine Version (ENG03611) %s\n", buffer);

    memset(buffer, 0, BUFLEN);
    scanner_engineVersion(buffer, 2);
    printf("INFO: Engine Version (ENG05394) %s\n", buffer);

    if (!scanner_open()) {
        printf("ERROR: Failed to open scanner device\n");
        goto EXIT;
    }
    printf("INFO: EOBD OPEN OK\n");

    scanner_enableLog(1);
    printf("INFO: Log enabled\n");

    /* Flash reset */ {
        int result = scanner_flashResetNoWait();
        if (result != EOBD_SERIAL_OK)
        {
            char *msg;
            switch (result)
            {
            case EOBD_SERIAL_VOID: {
                msg = "EOBD_SERIAL_VOID";
            } break;
            case EOBD_SERIAL_ERROR: {
                msg = "EOBD_SERIAL_ERROR";
            } break;
            case EOBD_SERIAL_BAD_PARAMETER: {
                msg = "EOBD_SERIAL_BAD_PARAMETER";
            } break;
            case EOBD_SERIAL_MISSING_PAYMENT: {
                msg = "EOBD_SERIAL_MISSING_PAYMENT";
            } break;
            case EOBD_SERIAL_MISSING_ENGINEFILE: {
                msg = "EOBD_SERIAL_MISSING_ENGINEFILE";
            } break;
            }
            printf("ERROR: Failed to reset scanner - %s\n", msg);
            goto EXIT;
        }

        printf("INFO: Flash reset OK\n");
    }

    scanner_enableLogEcu(1);
    printf("INFO: Log ECU enabled\n");

    EOBD_FLASHINFO_S info;
    /* Flash info */ {
        int result = scanner_flashInfo(&info);
        if (result != EOBD_SERIAL_OK)
        {
            char *msg;
            switch (result)
            {
            case EOBD_SERIAL_VOID: {
                msg = "EOBD_SERIAL_VOID";
            } break;
            case EOBD_SERIAL_ERROR: {
                msg = "EOBD_SERIAL_ERROR";
            } break;
            case EOBD_SERIAL_BAD_PARAMETER: {
                msg = "EOBD_SERIAL_BAD_PARAMETER";
            } break;
            case EOBD_SERIAL_MISSING_PAYMENT: {
                msg = "EOBD_SERIAL_MISSING_PAYMENT";
            } break;
            case EOBD_SERIAL_MISSING_ENGINEFILE: {
                msg = "EOBD_SERIAL_MISSING_ENGINEFILE";
            } break;
            }
            printf("ERROR: Failed to get flash info - %s\n", msg);
            goto EXIT;
        }

        printf("INFO: Get flash info OK\n");
    }

    printf("INFO: SCANTOOL Software version %s\n", info.szVersionSoftware);
    printf("INFO: SCANTOOL Serial number %s\n", info.szSerialNumber);
    printf("INFO: SCANTOOL Device %s\n", info.szDateTime);

    int iLast = 6;
    for (int mm = 0; mm < iLast; ++mm) {
        int jj;
        switch (mm)
        { // CARS
        case 0: {
            jj = EOBD_PROTOCOL_ISO9141;
        } break;
        case 1: {
            jj = EOBD_PROTOCOL_CANISO15765;
        } break;
        case 2: {
            jj = EOBD_PROTOCOL_KW2000;
        } break;
        case 3: {
            jj = EOBD_PROTOCOL_J1850PWM;
        } break;
        case 4: {
            jj = EOBD_PROTOCOL_J1850VPW;
        } break;
        case 5: {
            jj = EOBD_PROTOCOL_ELECTRICAL_CONTINUITY;
        } break;
        }

        int result = scanner_testProtocol(jj, info.DeviceType);
        if (result == EOBD_SERIAL_OK)
        {
            if (jj == EOBD_PROTOCOL_ELECTRICAL_CONTINUITY)
            {
                printf("INFO: TEST ELECTRICAL CONTINUITY PASS\n");
                break;
            }

            switch (jj)
            {
            case EOBD_PROTOCOL_ISO27145: {
                printf("INFO: PROTOCOL ISO27145 DETECTED\n");
            } break;
            case EOBD_PROTOCOL_J1939: {
                printf("INFO: PROTOCOL J1939 DETECTED\n");
            } break;
            case EOBD_PROTOCOL_CANISO15765: {
                printf("INFO: PROTOCOL CAN DETECTED\n");
            } break;
            case EOBD_PROTOCOL_ISO9141: {
                printf("INFO: PROTOCOL ISO9141 DETECTED\n");
            } break;
            case EOBD_PROTOCOL_KW2000: {
                printf("INFO: PROTOCOL KW2000 DETECTED\n");
            } break;
            case EOBD_PROTOCOL_J1850PWM: {
                printf("INFO: PROTOCOL J1850PWM DETECTED\n");
            } break;
            case EOBD_PROTOCOL_J1850VPW: {
                printf("INFO: PROTOCOL J1850VPW DETECTED\n");
            } break;
            }

            int vetEOBD[EOBD_TOTAL_PID] = { 0 };
            char szParameter[512] = { 0 };
            int nrError = 0;

            if (scanner_fastSupportFast(vetEOBD) != EOBD_SERIAL_OK)
            {
                printf("ERROR: EOBD SCANTOOL NOT CONNECTED\n");
                break;
            }

            if (vetEOBD[EOBD_PID_DIAGNOSI_INFO])
            {
                if (scanner_readParameter(szParameter, EOBD_PID_DIAGNOSI_INFO) == EOBD_SERIAL_OK)
                {
                    printf("INFO: PROTOCOL DETECTED %s\n", szParameter);
                }
                else
                {
                    printf("ERROR: EOBD SCANTOOL NOT CONNECTED\n");
                    break;
                }
            }

            if (vetEOBD[EOBD_PID_VIN])
            {
                if (scanner_readParameter(szParameter, EOBD_PID_VIN) == EOBD_SERIAL_OK)
                {
                    printf("INFO: VIN %s\n", szParameter);
                }
                else
                {
                    printf("ERROR: EOBD SCANTOOL NOT CONNECTED\n");
                    break;
                }
            }
            else
            {
                scanner_readVIN(szParameter);
                printf("INFO: VIN %s\n", szParameter);
            }

            if (scanner_getErrors(&nrError, szParameter, (EOBD_PROTOCOL_E)jj) == EOBD_SERIAL_OK)
            {
                if (nrError > 0)
                {
                    printf("INFO: ERORR LIST %s\n", szParameter);
                }
            }
            else
            {
                printf("ERROR: EOBD SCANTOOL NOT CONNECTED\n");
                break;
            }

            if (scanner_statusMil(&nrError) == EOBD_SERIAL_OK)
            {
                char* msg;
                switch (nrError)
                {
                case MIL_ON:  {
                    msg = "MIL ON";
                } break;
                case MIL_OFF: {
                    msg = "MIL OFF";
                } break;
                case MIL_BLINK: {
                    msg = "MIL FLASH";
                } break;
                default: {
                    msg = "MIL SAE RESERVED";
                } break;
                }
                printf("INFO: MIL STATUS %s\n", msg);
            }
            else
            {
                printf("ERROR: EOBD SCANTOOL NOT CONNECTED\n");
                break;
            }

            if (vetEOBD[EOBD_PID_EOBD_STATUS_1C])
            {
                if (scanner_readParameter(szParameter, EOBD_PID_EOBD_STATUS_1C) == EOBD_SERIAL_OK)
                {
                    printf("INFO: EOBD STATUS %s\n", szParameter);
                }
                else
                {
                    printf("ERROR: EOBD SCANTOOL NOT CONNECTED\n");
                    break;
                }
            }

            if (vetEOBD[EOBD_PID_RDNSUPP_FE])
            {
                if (scanner_readParameter(szParameter, EOBD_PID_RDNSUPP_FE) == EOBD_SERIAL_OK)
                {
                    printf("INFO: READINESS TEST SUPPORTED %s\n", szParameter);
                }
                else
                {
                    printf("ERROR: EOBD SCANTOOL NOT CONNECTED\n");
                    break;
                }
            }

            if (vetEOBD[EOBD_PID_RDNCMPL_FF])
            {
                if (scanner_readParameter(szParameter, EOBD_PID_RDNCMPL_FF) == EOBD_SERIAL_OK)
                {
                    printf("INFO: READINESS TEST COMPLETED %s\n", szParameter);
                }
                else
                {
                    printf("ERROR: EOBD SCANTOOL NOT CONNECTED\n");
                    break;
                }
            }

            if (vetEOBD[EOBD_PID_KM_MILON_21])
            {
                if (scanner_readParameter(szParameter, EOBD_PID_KM_MILON_21) == EOBD_SERIAL_OK)
                {
                    printf("INFO: KM WITH MIL ON %s\n", szParameter);
                }
                else
                {
                    printf("ERROR: EOBD SCANTOOL NOT CONNECTED\n");
                    break;
                }
            }

            if (vetEOBD[EOBD_PID_TIME_MILON_4D])
            {
                if (scanner_readParameter(szParameter, EOBD_PID_TIME_MILON_4D) == EOBD_SERIAL_OK)
                {
                    printf("INFO: TIME WITH MIL ON %s\n", szParameter);
                }
                else
                {
                    printf("ERROR: EOBD SCANTOOL NOT CONNECTED\n");
                    break;
                }
            }

            if (vetEOBD[EOBD_PID_CALID])
            {
                if (scanner_readParameter(szParameter, EOBD_PID_CALID) == EOBD_SERIAL_OK)
                {
                    printf("INFO: CALID HOMOLOGATION ECU %s\n", szParameter);
                }
                else
                {
                    printf("ERROR: EOBD SCANTOOL NOT CONNECTED\n");
                    break;
                }
            }
            else
            {
                scanner_readCALID(szParameter);
                printf("INFO: CALID HOMOLOGATION ECU %s\n", szParameter);
            }

            if (vetEOBD[EOBD_PID_AVG_AGENT_85])
            {
                if (scanner_readParameter(szParameter, EOBD_PID_AVG_AGENT_85) == EOBD_SERIAL_OK)
                {
                    printf("INFO: AVERAGE USAGE AGENT CATALYZATEUR %s\n", szParameter);
                }
                else
                {
                    printf("ERROR: EOBD SCANTOOL NOT CONNECTED\n");
                    break;
                }
            }

            if (vetEOBD[EOBD_PID_PED_ACCEL_49])
            {
                if (scanner_readParameter(szParameter, EOBD_PID_PED_ACCEL_49) == EOBD_SERIAL_OK)
                {
                    printf("INFO: ACCELERATOR PEDAL POSITION %s\n", szParameter);
                }
                else
                {
                    printf("ERROR: EOBD SCANTOOL NOT CONNECTED\n");
                    break;
                }
            }

            if (vetEOBD[EOBD_PID_ECUADDRESS])
            {
                if (scanner_readParameter(szParameter, EOBD_PID_ECUADDRESS) == EOBD_SERIAL_OK)
                {
                    printf("INFO: ECU ADDRESS %s\n", szParameter);
                }
                else
                {
                    printf("ERROR: EOBD SCANTOOL NOT CONNECTED\n");
                    break;
                }
            }

            if (vetEOBD[EOBD_PID_TOTAL_DISTANCE_TRAVELED_1701])
            {
                if (scanner_readParameter(szParameter, EOBD_PID_TOTAL_DISTANCE_TRAVELED_1701) == EOBD_SERIAL_OK)
                {
                    printf("INFO: TOTAL DISTANCE TRAVELED %s\n", szParameter);
                }
                else
                {
                    printf("ERROR: EOBD SCANTOOL NOT CONNECTED\n");
                    break;
                }
            }

            if (vetEOBD[EOBD_PID_TOTAL_FUEL_CONSUMED_1702])
            {
                if (scanner_readParameter(szParameter, EOBD_PID_TOTAL_FUEL_CONSUMED_1702) == EOBD_SERIAL_OK)
                {
                    printf("INFO: TOTAL FUEL CONSUMED %s\n", szParameter);
                }
                else
                {
                    printf("ERROR: EOBD SCANTOOL NOT CONNECTED\n");
                    break;
                }
            }

            if (vetEOBD[EOBD_PID_TOTAL_DISTANCE_ENGINE_OFF_1A01])
            {
                if (scanner_readParameter(szParameter, EOBD_PID_TOTAL_DISTANCE_ENGINE_OFF_1A01) == EOBD_SERIAL_OK)
                {
                    printf("INFO: TOTAL DISTANCE TRAVELLED IN CHARGE DEPLETION OPERATION WITH ENGINE OFF %s\n", szParameter);
                }
                else
                {
                    printf("ERROR: EOBD SCANTOOL NOT CONNECTED\n");
                    break;
                }
            }

            if (vetEOBD[EOBD_PID_TOTAL_DISTANCE_ENGINE_RUN_1A02])
            {
                if (scanner_readParameter(szParameter, EOBD_PID_TOTAL_DISTANCE_ENGINE_RUN_1A02) == EOBD_SERIAL_OK)
                {
                    printf("INFO: TOTAL DISTANCE TRAVELLED IN CHARGE DEPLETION OPERATION WITH ENGINE RUNNING %s\n", szParameter);
                }
                else
                {
                    printf("ERROR: EOBD SCANTOOL NOT CONNECTED\n");
                    break;
                }
            }

            if (vetEOBD[EOBD_PID_TOTAL_DISTANCE_SELECTABLE_1A03])
            {
                if (scanner_readParameter(szParameter, EOBD_PID_TOTAL_DISTANCE_SELECTABLE_1A03) == EOBD_SERIAL_OK)
                {
                    printf("INFO: TOTAL DISTANCE TRAVELLED IN DRIVER SELECTABLE CHARGE INCREASING %s\n", szParameter);
                }
                else
                {
                    printf("ERROR: EOBD SCANTOOL NOT CONNECTED\n");
                    break;
                }
            }

            if (vetEOBD[EOBD_PID_TOTAL_FUEL_CHARGE_DEPLETING_1B01])
            {
                if (scanner_readParameter(szParameter, EOBD_PID_TOTAL_FUEL_CHARGE_DEPLETING_1B01) == EOBD_SERIAL_OK)
                {
                    printf("INFO: TOTAL FUEL CONSUMED IN CHARGE DEPLETION OPERATION %s\n", szParameter);
                }
                else
                {
                    printf("ERROR: EOBD SCANTOOL NOT CONNECTED\n");
                    break;
                }
            }

            if (vetEOBD[EOBD_PID_TOTAL_FUEL_SELECTABLE_CHARGE_1B02])
            {
                if (scanner_readParameter(szParameter, EOBD_PID_TOTAL_FUEL_SELECTABLE_CHARGE_1B02) == EOBD_SERIAL_OK)
                {
                    printf("INFO: TOTAL FUEL CONSUMED IN DRIVER SELECTABLE CHARGE INCREASING %s\n", szParameter);
                }
                else
                {
                    printf("ERROR: EOBD SCANTOOL NOT CONNECTED\n");
                    break;
                }
            }

            if (vetEOBD[EOBD_PID_TOTAL_GRID_ENERGY_BATTERY_1C01])
            {
                if (scanner_readParameter(szParameter, EOBD_PID_TOTAL_GRID_ENERGY_BATTERY_1C01) == EOBD_SERIAL_OK)
                {
                    printf("INFO: TOTAL GRID ENERGY INTO THE BATTERY %s\n", szParameter);
                }
                else
                {
                    printf("ERROR: EOBD SCANTOOL NOT CONNECTED\n");
                    break;
                }
            }

            int PID0C = vetEOBD[EOBD_PID_RPM_0C] != 0;
            int rpm = 0;
            if (scanner_fastRpm(&rpm, PID0C) == EOBD_SERIAL_OK)
            {
                printf("INFO: RPM ENGINE %d\n", rpm);
            }
            else
            {
                printf("ERROR: EOBD SCANTOOL NOT CONNECTED\n");
                break;
            }

            int tempPID = 0;
            int valTemp = 0;

            if (vetEOBD[EOBD_PID_TEMP_05])
            {
                tempPID = EOBD_TEMPPID_0500;
            }
            else if (vetEOBD[EOBD_PID_TEMP_67])
            {
                tempPID = EOBD_TEMPPID_6700;
            }
            else if (vetEOBD[EOBD_PID_TEMP_6701])
            {
                tempPID = EOBD_TEMPPID_6701;
            }

            if (scanner_fastTemp(&valTemp, tempPID) == EOBD_SERIAL_OK)
            {
                printf("INFO: ENGINE TEMPERATURE %d\n", valTemp);
            }
            else
            {
                printf("ERROR: EOBD SCANTOOL NOT CONNECTED\n");
                break;
            }

            mm = iLast - 1;
            scanner_flashResetNoWait();
            scanner_enableLogEcu(1);
        }
        else if (result == EOBD_SERIAL_VOID)
        {
            printf("ERROR: Failed test protocol - EOBD_SERIAL_VOID\n");
            break;
        }
        else if (result == EOBD_PROTOCOL_ELECTRICAL_CONTINUITY)
        {
            printf("ERROR: Electrical continuity test failled\n");
            break;
        }
        else
        {
            switch (jj)
            {
            case EOBD_PROTOCOL_ISO27145: {
                printf("ERROR: PROTOCOL ISO27145 NOT SUPPORTED\n");
            } break;
            case EOBD_PROTOCOL_J1939: {
                printf("ERROR: PROTOCOL J1939 NOT SUPPORTED\n");
            } break;
            case EOBD_PROTOCOL_CANISO15765: {
                printf("ERROR: PROTOCOL CAN NOT SUPPORTED\n");
            } break;
            case EOBD_PROTOCOL_ISO9141: {
                printf("ERROR: PROTOCOL ISO9141 NOT SUPPORTED\n");
            } break;
            case EOBD_PROTOCOL_KW2000: {
                printf("ERROR: PROTOCOL KW2000 NOT SUPPORTED\n");
            } break;
            case EOBD_PROTOCOL_J1850PWM: {
                printf("ERROR: PROTOCOL J1850PWM NOT SUPPORTED\n");
            } break;
            case EOBD_PROTOCOL_J1850VPW: {
                printf("ERROR: PROTOCOL J1850VPW NOT SUPPORTED\n");
            } break;
            }
        }
    }

EXIT:
    scanner_close();
    scanner_unloadDll();
    data->status = J_STOPPED;
    return 0;
}

int InitScanner(void)
{
    handle = LoadLibrary("EobdCom.dll");
    if (!handle) {
        return 0;
    }

    scanner_loadDLL = (fptr_int_int)GetProcAddress(handle, "EOBD_LoadDLL");
    if (!scanner_loadDLL) return 0;

    scanner_versionDll = (fptr_void_str)GetProcAddress(handle, "EOBD_VersionDLL");
    if (!scanner_versionDll) return 0;

    scanner_engineVersion = (fptr_int_str_int)GetProcAddress(handle, "EOBD_EngineVersion");
    if (!scanner_engineVersion) return 0;

    scanner_unloadDll = (fptr_void_void)GetProcAddress(handle, "EOBD_UnloadDLL");
    if (!scanner_unloadDll) return 0;

    scanner_open = (fptr_int_void)GetProcAddress(handle, "EOBD_Open");
    if (!scanner_open) return 0;

    scanner_close = (fptr_void_void)GetProcAddress(handle, "EOBD_Close");
    if (!scanner_close) return 0;

    scanner_enableLog = (fptr_void_int)GetProcAddress(handle, "EOBD_EnableLog");
    if (!scanner_enableLog) return 0;

    scanner_enableLogEcu = (fptr_void_int)GetProcAddress(handle, "EOBD_EnableLogECU");
    if (!scanner_enableLogEcu) return 0;

    scanner_flashResetNoWait = (fptr_int_void)GetProcAddress(handle, "EOBD_FlashResetNoWait");
    if (!scanner_flashResetNoWait) return 0;

    scanner_flashInfo = (fptr_int_info)GetProcAddress(handle, "EOBD_FlashInfo");
    if (!scanner_flashInfo) return 0;

    scanner_testProtocol = (fptr_int_int_int)GetProcAddress(handle, "EOBD_TestProtocol");
    if (!scanner_testProtocol) return 0;

    scanner_getProtocol = (fptr_void_str)GetProcAddress(handle, "EOBD_GetProtocol");
    if (!scanner_getProtocol) return 0;

    scanner_statusMil = (fptr_int_intptr)GetProcAddress(handle, "EOBD_StatusMIL");
    if (!scanner_statusMil) return 0;

    scanner_activeIncDelay = (fptr_void_int_ul)GetProcAddress(handle, "EOBD_ActiveIncDelay");
    if (!scanner_activeIncDelay) return 0;

    scanner_readParameter = (fptr_int_str_int)GetProcAddress(handle, "EOBD_ReadParameter");
    if (!scanner_readParameter) return 0;

    scanner_readVIN = (fptr_void_str)GetProcAddress(handle, "EOBD_ReadVIN");
    if (!scanner_readVIN) return 0;

    scanner_readCALID = (fptr_void_str)GetProcAddress(handle, "EOBD_ReadCALID");
    if (!scanner_readCALID) return 0;

    scanner_getErrors = (fptr_int_intptr_str_protocol)GetProcAddress(handle, "EOBD_GetErrors");
    if (!scanner_getErrors) return 0;

    scanner_fastRpm = (fptr_int_intptr_int)GetProcAddress(handle, "EOBD_FastRpm");
    if (!scanner_fastRpm) return 0;

    scanner_fastTemp = (fptr_int_intptr_int)GetProcAddress(handle, "EOBD_FastTemp");
    if (!scanner_fastTemp) return 0;

    scanner_fastSupportFast = (fptr_int_intptr)GetProcAddress(handle, "EOBD_FastSupportFast");
    if (!scanner_fastSupportFast) return 0;

    scanner_stopPeriodicRpmTemp = (fptr_int_void)GetProcAddress(handle, "EOBD_StopPeriodicRpmTemp");
    if (!scanner_stopPeriodicRpmTemp) return 0;

    scanner_restartPeriodicRpmTemp = (fptr_int_void)GetProcAddress(handle, "EOBD_RestartPeriodicRpmTemp");
    if (!scanner_restartPeriodicRpmTemp) return 0;

    return 1;
}

void StartScanner(AppData* data)
{
    DWORD threadId;
    data->status = J_RUNNING;
    jobThreadHandle = CreateThread(0, 0, jobThread, (void*)data, 0, &threadId);
}
