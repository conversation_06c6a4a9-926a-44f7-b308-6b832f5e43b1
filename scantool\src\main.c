#include <raylib.h>

#define RAYGUI_IMPLEMENTATION
#include <raygui.h>
#include <job.h>

internal Texture2D logo;

internal AppSettings* loadSettings()
{
    bool exists = FileExists("settings.bin");
    AppSettings* result = 0;

    if (exists)
    {
        int bytesRead = 0;
        char* data = LoadFileData("settings.bin", &bytesRead);

        if (data)
        {
            if (bytesRead != sizeof(AppSettings))
            {
                RL_FREE(data);
            }
            else
            {
                result = (AppSettings*)data;
            }
        }
    }

    return result;
}

internal bool saveSettings(AppSettings* settings)
{
    bool result = false;
    result = SaveFileData("settings.bin", (char*)settings, sizeof(AppSettings));
    return result;
}

internal AppStatus screenSettings(AppData* data)
{
    AppSettings* settings = data->settings;

    // SCANNER COM
    GuiLabel((Rectangle) { 10, 10, 110, 24 }, "Porta:");
    GuiListView((Rectangle) { 110, 10, 120, 120 }, "COM1;COM2;COM3;COM4;COM5;COM6;COM7;COM8;COM9;COM10;COM11;COM12;COM13;COM14;COM15;COM16;COM17;COM18;COM19;COM20", &settings->scannerScroll, &settings->scannerCom);

    // HTTP PORT
    GuiLabel((Rectangle) { 250, 10, 110, 24 }, "Porta HTTP:");
    if (GuiTextBox((Rectangle) { 350, 10, 120, 24 }, settings->httpPort, 8, settings->httpPortStatus))
    {
        settings->httpPortStatus = 1;
        settings->usernameStatus = 0;
        settings->passwordStatus = 0;
    }

    // USERNAME
    GuiLabel((Rectangle) { 250, 40, 110, 24 }, "Utilizador:");
    if (GuiTextBox((Rectangle) { 350, 40, 120, 24 }, settings->username, 64, settings->usernameStatus))
    {
        settings->httpPortStatus = 0;
        settings->usernameStatus = 1;
        settings->passwordStatus = 0;
    }

    // PASSWORD
    GuiLabel((Rectangle) { 250, 70, 110, 24 }, "Password:");
    if (GuiTextBox((Rectangle) { 350, 70, 120, 24 }, settings->password, 64, settings->passwordStatus))
    {
        settings->httpPortStatus = 0;
        settings->usernameStatus = 0;
        settings->passwordStatus = 1;
    }

    if (GuiButton((Rectangle) { 540, 360-34, 90, 24 }, "Guardar"))
    {
        saveSettings(settings);
        return SCREEN_MAIN;
    }

    return SCREEN_SETTINGS;
}

internal AppStatus screenMain(AppData* data)
{
    int width = GetRenderWidth();
    int height = GetRenderHeight();

    DrawTexture(logo, 20, 20, WHITE);

    bool isTesting = data->status == J_RUNNING;
    if (isTesting) {
        GuiDisable();
    } else {
        GuiEnable();
    }

    if (GuiButton((Rectangle) {(float)width-110.0f, 10.0f, 90.0f, 24.0f }, "Configurar"))
    {
        return SCREEN_SETTINGS;
    }

    if (GuiButton((Rectangle) { (float)width-110.0f, 40.0f, 90.0f, 24.0f}, "Iniciar Leitura"))
    {
        StartScanner(data);
    }

    if (!ServerThreadIsRunning())
    {
        StartServerThread(data);
    }

    AppSettings* settings = data->settings;

    int y = height - 20;
    int x = 10;
    DrawRectangle(x, y, 10, 10, ServerThreadIsRunning() ? GREEN : (ServerThreadIsError() ? RED : BLUE));
    GuiLabel((Rectangle) {(float)x+15.0f, (float)y-5.0f, 100.0f, 24.0f
    }, "Servidor HTTP");

    x = width-70;
    y = height - 16;
    GuiLabel((Rectangle) {(float)x, (float)y-5.0f, 70.0f, 24.0f }, "by gobox.pt");

    return SCREEN_MAIN;
}

int main(void)
{
    InitWindow(640, 360, "Gesauto: Scantool - by gobox.pt");
    SetWindowMinSize(640, 360);
    SetWindowState(FLAG_WINDOW_RESIZABLE);

    SetTargetFPS(30);

    AppStatus status = SCREEN_MAIN;
    AppData data = {0};

    if (!InitScanner()) {
        TraceLog(LOG_FATAL, "Failed to load DLL");
        return -1;
    }

    data.settings = loadSettings();
    if (!data.settings)
    {
        data.settings = (AppSettings*)MemAlloc(sizeof(AppSettings));
        data.settings->scannerCom = 0;
        data.settings->scannerScroll = 0;
        saveSettings(data.settings);

        status = SCREEN_SETTINGS;
    }

    logo = LoadTexture("gesauto.png");

    while (!WindowShouldClose())
    {
        BeginDrawing();
        ClearBackground(GetColor(GuiGetStyle(DEFAULT, BACKGROUND_COLOR)));

        switch (status)
        {
        case SCREEN_SETTINGS: {
            status = screenSettings(&data);
        } break;
        case SCREEN_MAIN: {
            status = screenMain(&data);
        } break;
        }

        EndDrawing();
    }

    return 0;
}