#include "EobdSeriale.h"

char            szVersion[32];
bool            bEnableEcu = false;
int             iEOBDSerialPort;        // EOBD serial port number

// Load EOBD DLL
iEOBDSerialPort = 1
// param IN (iEOBDSerialPort) : int <- [EOBD serial port number]
EOBD_LoadDLL(1);

// INFO EOBD DLL
// param OUT (szVersion) : [char] -> "EOBD DLL VERSION" ex "1.017"
EOBD_VersionDLL(szVersion);

// INFO ENG00051.BBZ FILE
// param IN (0) : int              <- [select ENG00051.BBZ file]
// param OUT (szVersion)  : [char] -> "ENG00051.BBZ file VERSION" ex "2548"
EOBD_EngineVersion(szVersion, 0);

// INFO ENG03611.BBZ FILE
// param IN (1)  : int             <- [select ENG03611.BBZ file]
// param OUT (szVersion)  : [char] -> "ENG03611.BBZ file VERSION" ex "1494"
EOBD_EngineVersion(szVersion, 1);

// INFO ENG05394.BBZ FILE
// param IN (2) : int              <- [select ENG05394.BBZ file]
// param OUT (szVersion)  : [char] -> "ENG05394.BBZ file VERSION" ex "1019"
EOBD_EngineVersion(szVersion, 2);

// OPEN EOBD SERIAL PORT
if (EOBD_Open() == false) {
    // Operation failed
    -> VIDEO MESSAGE        "EOBD SERIAL PORT NOT AVAILABLE"

    goto EXIT;
} else {
    // Operation successful
    char                szListError[512]   = "";
    char                szParameter[256]   = "";
    int                 iNrError           = 0;
    int                 iMILStatus;
    int                 m_VetEOBD[EOBD_TOTAL_PID] = { 0 };
    bool                bPID0C;
    int                 iRpm = 0;
    int                 nVALTemp_OBD = 0;
    int                 nTemp_PID;
    int                 iRet;
    int                 mm;
    EOBD_FLASHINFO_S    eobdFlashInfo;

    // ENABLE EOBD COMUNICATION LOG FILE CREATION
    // param IN (true)  : bool  <- [create file "LogEOBD.txt" on filesystem]
    // param IN (false) : bool  <- [no log file creation]
    EOBD_EnableLog(true);

    // RESET ENGINE WITHIN THE SCANTOOL
    iRet = EOBD_FlashResetNoWait();
    if (iRet != EOBD_SERIAL_OK) {
        -> VIDEO MESSAGE    "EOBD SCANTOOL NOT CONNECTED"

        goto EXIT;
    }

    // ENABLE ECU LOG FILE CREATION
    // param IN (bEnableEcu if true)  : bool  <- [create file "LogECU.txt" on filesystem]
    // param IN (bEnableEcu if false) : bool  <- [no log file creation]
    EOBD_EnableLogECU(bEnableEcu);

    // READING SCANTOOL INFORMATIONS
    // param OUT (eobdFlashInfo) : EOBD_FLASHINFO_S  <- [structure used for reading scantool informations]
    iRet = EOBD_FlashInfo(&eobdFlashInfo);
    if (iRet == EOBD_SERIAL_OK) {
        -> VIDEO MESSAGE    "SCANTOOL Software version: " + String(eobdFlashInfo.szVersionSoftware)
        -> VIDEO MESSAGE    "SCANTOOL Serial number: "    + String(eobdFlashInfo.szSerialNumber)
        -> VIDEO MESSAGE    "SCANTOOL Device: "           + String(eobdFlashInfo.szDateTime)
    } else {
        -> VIDEO MESSAGE    "EOBD SCANTOOL NOT CONNECTED"

        goto EXIT;
    }
}

// Select only one definition of the 3 following definitions:
// #define TESTVL
#define TESTPLEURO5
// #define TESTPLEURO6

#ifdef TESTVL                       // for CAR
   int iLast = 6;
#elif defined(TESTPLEURO5)          // for TRUCK EURO5
   int iLast = 7;
#elif defined(TESTPLEURO6)          // for TRUCK EURO6
   int iLast = 4;
#else
   #error "Define only one !!!"
#endif

   for (mm = 0; mm < iLast; ++mm) {
        int         jj;

#ifdef TESTVL
        switch (mm) {   // CAR
            case 0:  jj = EOBD_PROTOCOL_ISO9141;                break;
            case 1:  jj = EOBD_PROTOCOL_CANISO15765;            break;
            case 2:  jj = EOBD_PROTOCOL_KW2000;                 break;
            case 3:  jj = EOBD_PROTOCOL_J1850PWM;               break;
            case 4:  jj = EOBD_PROTOCOL_J1850VPW;               break;
            case 5:  jj = EOBD_PROTOCOL_ELECTRICAL_CONTINUITY;  break;
        }
#endif

#ifdef TESTPLEURO5
        switch (mm) {   // TRUCK EURO5
            case 0:  jj = EOBD_PROTOCOL_J1939;                  break;
            case 1:  jj = EOBD_PROTOCOL_ISO9141;                break;
            case 2:  jj = EOBD_PROTOCOL_CANISO15765;            break;
            case 3:  jj = EOBD_PROTOCOL_KW2000;                 break;
            case 4:  jj = EOBD_PROTOCOL_J1850PWM;               break;
            case 5:  jj = EOBD_PROTOCOL_J1850VPW;               break;
            case 6:  jj = EOBD_PROTOCOL_ELECTRICAL_CONTINUITY;  break;
        }
#endif

#ifdef TESTPLEURO6
        switch (mm) {   // TRUCK EURO6
            case 0:  jj = EOBD_PROTOCOL_J1939;                  break;
            case 1:  jj = EOBD_PROTOCOL_ISO27145;               break;
            case 2:  jj = EOBD_PROTOCOL_CANISO15765;            break;
            case 3:  jj = EOBD_PROTOCOL_ELECTRICAL_CONTINUITY;  break;
        }
#endif

        // PROBE EOBD PROTOCOL
        // param IN (jj) : int  <- [EOBD protocoll to detect]
        // param IN (eobdFlashInfo.DeviceType) : EOBD_DEVICE_E  <- [Scantool device detected]
        iRet = EOBD_TestProtocol(jj, eobdFlashInfo.DeviceType);
        if (iRet == EOBD_SERIAL_OK) {
            if (jj == EOBD_PROTOCOL_ELECTRICAL_CONTINUITY) {
                -> VIDEO MESSAGE        "TEST ELECTRICAL CONTINUITY PASS"
                break;
            }

            switch (jj) {
                case EOBD_PROTOCOL_ISO27145:
                    -> VIDEO MESSAGE        "PROTOCOL ISO27145 DETECTED"
                    break;
                case EOBD_PROTOCOL_J1939:
                    -> VIDEO MESSAGE        "PROTOCOL J1939 DETECTED"
                    break,
                case EOBD_PROTOCOL_CANISO15765:
                    -> VIDEO MESSAGE        "PROTOCOL CAN DETECTED"
                    break;
                case EOBD_PROTOCOL_ISO9141:
                    -> VIDEO MESSAGE        "PROTOCOL ISO9141 DETECTED"
                    break;
                case EOBD_PROTOCOL_KW2000:
                    -> VIDEO MESSAGE        "PROTOCOL KW2000 DETECTED"
                    break;
                case EOBD_PROTOCOL_J1850PWM:
                    -> VIDEO MESSAGE        "PROTOCOL J1850PWM DETECTED"
                    break;
                case EOBD_PROTOCOL_J1850VPW:
                    -> VIDEO MESSAGE        "PROTOCOL J1850VPW DETECTED"
                    break;
            }

            // READING EOBD PIDS SUPPORTED ON VEHICLE
            // param OUT (m_VetEOBD) : [int]  -> [array of PIDS supported]
            iRet = EOBD_FastSupportFast(m_VetEOBD);
            if (iRet != EOBD_SERIAL_OK) {
                -> VIDEO MESSAGE        "EOBD SCANTOOL NOT CONNECTED"
                break;
            }

            // READ PROTOCOL INFO
            if (m_VetEOBD[EOBD_PID_DIAGNOSI_INFO]) {
                // param OUT (szParameter) : [char] -> [parameter read from ECU]
                // param IN (EOBD_PID_DIAGNOSI_INFO) : int  <- [PID selected to read from ECU]
                if (EOBD_ReadParameter(szParameter, EOBD_PID_DIAGNOSI_INFO) == EOBD_SERIAL_OK) {
                    -> VIDEO MESSAGE    "PROTOCOL DETECTED : " + String(szParameter)
                } else {
                    -> VIDEO MESSAGE    "EOBD SCANTOOL NOT CONNECTED"
                    break;
                }
            }

            // READ VIN
            if (m_VetEOBD[EOBD_PID_VIN]) {
                // param OUT (szParameter) : [char] -> [parameter read from ECU]                 
                // param IN (EOBD_PID_VIN) : int  <- [PID selected to read from ECU]                 
                if (EOBD_ReadParameter(szParameter, EOBD_PID_VIN) == EOBD_SERIAL_OK) {
                    -> VIDEO MESSAGE    "VIN : " + String(szParameter);
                } else {
                    -> VIDEO MESSAGE    "EOBD SCANTOOL NOT CONNECTED"
                    break;
                }
            } else {
                // param OUT (szParameter) : [char] -> [parameter read from ECU]                 
                if (EOBD_ReadVIN(szParameter) == EOBD_SERIAL_OK) {
                    -> VIDEO MESSAGE    "VIN : " + String(szParameter);
                } else {
                    -> VIDEO MESSAGE    "EOBD SCANTOOL NOT CONNECTED"
                    break;
                }
            }

            // READ NUMBER OF ERRORS AND ERROR CODES LIST
            // param OUT (iNrError) : int  -> [number of errors presents within ECU]
            // param OUT (szListError) : [char]  -> [error codes list]                 
            // param IN (jj) : int  <- [EOBD protocol detected]                 
            if (EOBD_GetErrors(&iNrError, szListError, (EOBD_PROTOCOL_E)jj) == EOBD_SERIAL_OK) {
                if (iNrError > 0) {
                    -> VIDEO MESSAGE    "ERORR LIST : " + String(szListError)
                }
            } else {
                -> VIDEO MESSAGE        "EOBD SCANTOOL NOT CONNECTED"
                break;
            }

            // READ MIL STATUS
            // param OUT (iMILStatus) : int -> [MIL status]
            if (EOBD_StatusMIL(&iMILStatus) == EOBD_SERIAL_OK) {
                String      Msg;

                if      (iMILStatus == MIL_ON) {
                    Msg = "MIL ON";
                } else if (iMILStatus == MIL_OFF) {
                    Msg = "MIL OFF";
                } else if (iMILStatus == MIL_FLASH) {
                    Msg = "MIL FLASH";
                } else {
                    Msg = "MIL SAE RESERVED";
                }

                -> VIDEO MESSAGE    "MIL STATUS : " + Msg;
            } else {
                -> VIDEO MESSAGE    "EOBD SCANTOOL NOT CONNECTED"
                break;
            }

            // READ EOBD STATUS
            if (m_VetEOBD[EOBD_PID_EOBD_STATUS_1C]) {
                // param OUT (szParameter) : [char] -> [parameter read from ECU]
                // param IN (EOBD_PID_EOBD_STATUS_1C) : int  <- [PID selected to read from ECU]
                if (EOBD_ReadParameter(szParameter, EOBD_PID_EOBD_STATUS_1C) == EOBD_SERIAL_OK) {
                    -> VIDEO MESSAGE    "EOBD STATUS : " + String(szParameter)
                } else {
                    -> VIDEO MESSAGE    "EOBD SCANTOOL NOT CONNECTED"
                    break;
                }
            }
            // READ READINES TEST SUPPORTED
            if (m_VetEOBD[EOBD_PID_RDNSUPP_FE]) {
                // param OUT (szParameter) : [char] -> [parameter read from ECU]
                // param IN (EOBD_PID_RDNSUPP_FE) : int  <- [PID selected to read from ECU]
                if (EOBD_ReadParameter(szParameter, EOBD_PID_RDNSUPP_FE) == EOBD_SERIAL_OK) {
                    -> VIDEO MESSAGE    "READINESS TEST SUPPORTED : " + String(szParameter)
                } else {
                    -> VIDEO MESSAGE    "EOBD SCANTOOL NOT CONNECTED"
                    break;
                }
            }
            // READ READINES TEST COMPLETED
            if (m_VetEOBD[EOBD_PID_RDNCMPL_FF]) {
                // param OUT (szParameter) : [char] -> [parameter read from ECU]
                // param IN (EOBD_PID_RDNCMPL_FF) : int <- [PID selected to read from ECU]
                if (EOBD_ReadParameter(szParameter, EOBD_PID_RDNCMPL_FF) == EOBD_SERIAL_OK) {
                    -> VIDEO MESSAGE    "READINESS TEST COMPLETED : " + String(szParameter)
                } else {
                    -> VIDEO MESSAGE    "EOBD SCANTOOL NOT CONNECTED"
                    break;
                }
            }

            // READ DISTANCE WITH MIL ON
            if (m_VetEOBD[EOBD_PID_KM_MILON_21]) {
                // param OUT (szParameter) : [char] -> [parameter read from ECU]
                // param IN (EOBD_PID_KM_MILON_21) : int  <- [PID selected to read from ECU]
                if (EOBD_ReadParameter(szParameter, EOBD_PID_KM_MILON_21) == EOBD_SERIAL_OK) {
                    -> VIDEO MESSAGE    "KM WITH MIL ON : " + String(szParameter)
                } else {
                    -> VIDEO MESSAGE    "EOBD SCANTOOL NOT CONNECTED"
                    break;
                }
            }

            // READING TIME INTERVALL WITH MIL ON
            if (m_VetEOBD[EOBD_PID_TIME_MILON_4D]) {
                // param OUT (szParameter) : [char] -> [parameter read from ECU]                 
                // param IN (...) : int  <- [PID selected to read from ECU]                 
                if (EOBD_ReadParameter(szParameter, EOBD_PID_TIME_MILON_4D) == EOBD_SERIAL_OK) {
                    -> VIDEO MESSAGE    "TIME WITH MIL ON : ") + String(szParameter)
                } else {
                    -> VIDEO MESSAGE    "EOBD SCANTOOL NOT CONNECTED"
                    break;
                }
            }

            // READ CALID
            if (m_VetEOBD[EOBD_PID_CALID]) {
                // param OUT (szParameter) : [char] -> [parameter read from ECU]                 
                // param IN (EOBD_PID_CALID) : int <- [PID selected to read from ECU]                 
                if (EOBD_ReadParameter(szParameter, EOBD_PID_CALID) == EOBD_SERIAL_OK)  {
                    -> VIDEO MESSAGE    "CALID HOMOLOGATION ECU : " + String(szParameter)
                } else {
                    -> VIDEO MESSAGE    "EOBD SCANTOOL NOT CONNECTED"
                    break;
                }
            } else {
                // param OUT (szParameter) : [char] -> [parameter read from ECU]
                if (EOBD_ReadCALID(szParameter) == EOBD_SERIAL_OK) {
                    -> VIDEO MESSAGE    "CALID HOMOLOGATION ECU : " + String(szParameter)
                } else {
                    -> VIDEO MESSAGE    "EOBD SCANTOOL NOT CONNECTED"
                    break;
                }
            }

            // READ AVERAGE USAGE AGENT
            if (m_VetEOBD[EOBD_PID_AVG_AGENT_85]) {
                // param OUT (szParameter) : [char] -> [parameter read from ECU]                 
                // param IN (EOBD_PID_AVG_AGENT_85) : int <- [PID selected to read from ECU]                 
                if (EOBD_ReadParameter(szParameter, EOBD_PID_AVG_AGENT_85) == EOBD_SERIAL_OK) {
                    -> VIDEO MESSAGE    "AVERAGE USAGE AGENT CATALYZATEUR : " + String(szParameter)
                } else {
                    -> VIDEO MESSAGE    "EOBD SCANTOOL NOT CONNECTED"
                    break;
                }
            }
            // READ ACCELERATOR PEDAL POSITION
            if (m_VetEOBD[EOBD_PID_PED_ACCEL_49]) {
                // param OUT (szParameter) : [char] -> [parameter read from ECU]
                // param IN (EOBD_PID_PED_ACCEL_49) : int <- [PID selected to read from ECU]
                if (EOBD_ReadParameter(szParameter, EOBD_PID_PED_ACCEL_49) == EOBD_SERIAL_OK) {
                    -> VIDEO MESSAGE    "ACCELERATOR PEDAL POSITION : " + String(szParameter)
                } else {
                    -> VIDEO MESSAGE    "EOBD SCANTOOL NOT CONNECTED"
                    break;
                }
            }

            // READ ECU ADDRESS
            if (m_VetEOBD[EOBD_PID_ECUADDRESS]) {
                // param OUT (szParameter) : [char] -> [parameter read from ECU]                 
                // param IN (EOBD_PID_ECUADDRESS) : int <- [PID selected to read from ECU]                 
                if (EOBD_ReadParameter(szParameter, EOBD_PID_ECUADDRESS) == EOBD_SERIAL_OK)  {
                    -> VIDEO MESSAGE    "ECU ADDRESS : " + String(szParameter)
                } else {
                    -> VIDEO MESSAGE    "EOBD SCANTOOL NOT CONNECTED"
                    break;
                }
            }

            // ----------------------------------   PIDS OBFCM   --------------------------------------

            // TOTAL DISTANCE TRAVELLED
            if (m_VetEOBD[EOBD_PID_TOTAL_DISTANCE_TRAVELED_1701]) {
                // param OUT (szParameter) : [char] -> [parameter read from ECU]
                // param IN (EOBD_PID_TOTAL_DISTANCE_TRAVELED_1701) : int <- [PID selected to read from ECU]                 
                if (EOBD_ReadParameter(szParameter, EOBD_PID_TOTAL_DISTANCE_TRAVELED_1701) == EOBD_SERIAL_OK) {
                    -> VIDEO MESSAGE    "TOTAL DISTANCE TRAVELED : " + String(szParameter)
                } else {
                    -> VIDEO MESSAGE    "EOBD SCANTOOL NOT CONNECTED"                        
                    break;
                }
            }

            // TOTAL FUEL CONSUMED
            if (m_VetEOBD[EOBD_PID_TOTAL_FUEL_CONSUMED_1702]) {
                // param OUT (szParameter) : [char] -> [parameter read from ECU]                 
                // param IN (EOBD_PID_TOTAL_FUEL_CONSUMED_1702) : int <- [PID selected to read from ECU]                 
                if (EOBD_ReadParameter(szParameter, EOBD_PID_TOTAL_FUEL_CONSUMED_1702) == EOBD_SERIAL_OK) {
                    -> VIDEO MESSAGE    "TOTAL FUEL CONSUMED : " + String(szParameter)
                } else {
                    -> VIDEO MESSAGE    "EOBD SCANTOOL NOT CONNECTED"    
                    break;
                }
            }

            // TOTAL DISTANCE TRAVELLED IN CHARGE DEPLETION OPERATION WITH ENGINE OFF
            if (m_VetEOBD[EOBD_PID_TOTAL_DISTANCE_ENGINE_OFF_1A01]) {
                // param OUT (szParameter) : [char] -> [parameter read from ECU]                 
                // param IN (EOBD_PID_TOTAL_DISTANCE_ENGINE_OFF_1A01) : int <- [PID selected to read from ECU]                 
                if (EOBD_ReadParameter(szParameter, EOBD_PID_TOTAL_DISTANCE_ENGINE_OFF_1A01) == EOBD_SERIAL_OK) {
                    -> VIDEO MESSAGE    "TOTAL DISTANCE TRAVELLED IN CHARGE DEPLETION OPERATION WITH ENGINE OFF : " + String(szParameter)
                } else {
                    -> VIDEO MESSAGE    "EOBD SCANTOOL NOT CONNECTED"
                    break;
                }
            }

            // TOTAL DISTANCE TRAVELLED IN CHARGE DEPLETION OPERATION WITH ENGINE RUNNING
            if (m_VetEOBD[EOBD_PID_TOTAL_DISTANCE_ENGINE_RUN_1A02]) {
                // param OUT (szParameter) : [char] -> [parameter read from ECU]                 
                // param IN (EOBD_PID_TOTAL_DISTANCE_ENGINE_RUN_1A02) : int <- [PID selected to read from ECU]                 
                if (EOBD_ReadParameter(szParameter, EOBD_PID_TOTAL_DISTANCE_ENGINE_RUN_1A02) == EOBD_SERIAL_OK) {
                    -> VIDEO MESSAGE    "TOTAL DISTANCE TRAVELLED IN CHARGE DEPLETION OPERATION WITH ENGINE RUNNING : " + String(szParameter)
                } else {
                    -> VIDEO MESSAGE    "EOBD SCANTOOL NOT CONNECTED"
                    break;
                }
            }

            // TOTAL DISTANCE TRAVELLED IN DRIVER SELECTABLE CHARGE INCREASING
            if (m_VetEOBD[EOBD_PID_TOTAL_DISTANCE_SELECTABLE_1A03]) {
                // param OUT (szParameter) : [char] -> [parameter read from ECU]
                // param IN (EOBD_PID_TOTAL_DISTANCE_SELECTABLE_1A03) : int <- [PID selected to read from ECU]                 
                if (EOBD_ReadParameter(szParameter, EOBD_PID_TOTAL_DISTANCE_SELECTABLE_1A03) == EOBD_SERIAL_OK) {
                    -> VIDEO MESSAGE    "TOTAL DISTANCE TRAVELLED IN DRIVER SELECTABLE CHARGE INCREASING : " + String(szParameter)
                } else {
                    -> VIDEO MESSAGE    "EOBD SCANTOOL NOT CONNECTED"
                    break;
                }
            }

            // TOTAL FUEL CONSUMED IN CHARGE DEPLETION OPERATION
            if (m_VetEOBD[EOBD_PID_TOTAL_FUEL_CHARGE_DEPLETING_1B01]) {
                // param OUT (szParameter) : [char] -> [parameter read from ECU]
                // param IN (EOBD_PID_TOTAL_FUEL_CHARGE_DEPLETING_1B01) : int  <- [PID selected to read from ECU]
                if (EOBD_ReadParameter(szParameter, EOBD_PID_TOTAL_FUEL_CHARGE_DEPLETING_1B01) == EOBD_SERIAL_OK) {
                    -> VIDEO MESSAGE    "TOTAL FUEL CONSUMED IN CHARGE DEPLETION OPERATION : " + String(szParameter)
                } else {
                    -> VIDEO MESSAGE    "EOBD SCANTOOL NOT CONNECTED"
                    break;
                }
            }

            // TOTAL FUEL CONSUMED IN DRIVER SELECTABLE CHARGE INCREASING
            if (m_VetEOBD[EOBD_PID_TOTAL_FUEL_SELECTABLE_CHARGE_1B02]) {
                // param OUT (szParameter) : [char] -> [parameter read from ECU]
                // param IN (EOBD_PID_TOTAL_FUEL_SELECTABLE_CHARGE_1B02) : int  <- [PID selected to read from ECU]                 
                if (EOBD_ReadParameter(szParameter, EOBD_PID_TOTAL_FUEL_SELECTABLE_CHARGE_1B02) == EOBD_SERIAL_OK) {
                    -> VIDEO MESSAGE    "TOTAL FUEL CONSUMED IN DRIVER SELECTABLE CHARGE INCREASING : " + String(szParameter)
                } else {
                    -> VIDEO MESSAGE    "EOBD SCANTOOL NOT CONNECTED"
                    break;
                }
            }

            // TOTAL GRID ENERGY INTO THE BATTERY
            if (m_VetEOBD[EOBD_PID_TOTAL_GRID_ENERGY_BATTERY_1C01]) {
                // param OUT (szParameter) : [char] -> [parameter read from ECU]
                // param IN (EOBD_PID_TOTAL_GRID_ENERGY_BATTERY_1C01) : int <- [PID selected to read from ECU]
                if (EOBD_ReadParameter(szParameter, EOBD_PID_TOTAL_GRID_ENERGY_BATTERY_1C01) == EOBD_SERIAL_OK) {
                    -> VIDEO MESSAGE    "TOTAL GRID ENERGY INTO THE BATTERY : " + String(szParameter)
                } else {
                    -> VIDEO MESSAGE    "EOBD SCANTOOL NOT CONNECTED"
                    break;
                }
            }
            //---------------------------------------------

            // READ ENGINE RPM
            if (m_VetEOBD[EOBD_PID_RPM_0C]) {
                bPID0C = true;
            } else {
                bPID0C = false;
            }

            // param OUT (iRpm) : int -> [Engine RPM]                 
            // param IN (bPID0C) : bool <- [availability of PID selected to read RPM from ECU]                 
            if (EOBD_FastRpm(&iRpm, bPID0C) == EOBD_SERIAL_OK) {
                -> VIDEO MESSAGE    "RPM ENGINE : " + IntToStr(iRpm)
            } else {
                -> VIDEO MESSAGE    "EOBD SCANTOOL NOT CONNECTED"
                break;
            }

            // READING TEMPERATURE
            if (m_VetEOBD[EOBD_PID_TEMP_05]) {
                nTemp_PID = EOBD_TEMPPID_0500;
            } else if (m_VetEOBD[EOBD_PID_TEMP_67]) {
                nTemp_PID = EOBD_TEMPPID_6700;
            } else if (m_VetEOBD[EOBD_PID_TEMP_6701]) {
                nTemp_PID = EOBD_TEMPPID_6701;
            }
            // param OUT (nVALTemp) : int -> [Engine temperature]                 
            // param IN (nTemp_PID) : int <- [PID selected to read Temperature from ECU]                 
            if (EOBD_FastTemp(&nVALTemp_OBD, nTemp_PID) == EOBD_SERIAL_OK) {
                -> VIDEO MESSAGE    "ENGINE TEMPERATURE : " + IntToStr(nVALTemp_OBD)
            } else {
                -> VIDEO MESSAGE    "EOBD SCANTOOL NOT CONNECTED"
                break;
            }

            // on first protocoll detected -> exit cycle
            mm = iLast - 1;

            // RESET ENGINE WITHIN THE SCANTOOL
            EOBD_FlashResetNoWait();

            // ENABLE ECU LOG FILE CREATION
            // param IN (bEnableEcu if true) : bool   <- [create file "LogECU.txt" on filesystem] 
            // param IN (bEnableEcu if false) : bool  <- [no log file creation]
            EOBD_EnableLogECU(bEnableEcu);
        } else {   // Protocoll Choosen Not Detected
            if (iRet == EOBD_SERIAL_VOID) {
                -> VIDEO MESSAGE    "EOBD SCANTOOL NOT CONNECTED"
                break;
            } else {
                if (jj == EOBD_PROTOCOL_ELECTRICAL_CONTINUITY) {
                    -> VIDEO MESSAGE    "TEST ELECTRICAL CONTINUITY FAIL"

                    break;
                }

                switch (jj) {
                    case EOBD_PROTOCOL_ISO27145:
                        -> VIDEO MESSAGE    "PROTOCOL ISO27145 NOT SUPPORTED"
                        break;

                    case EOBD_PROTOCOL_J1939:
                        -> VIDEO MESSAGE    "PROTOCOL J1939 NOT SUPPORTED"
                        break;

                    case EOBD_PROTOCOL_CANISO15765:
                        -> VIDEO MESSAGE    "PROTOCOL CAN NOT SUPPORTED"
                        break;

                    case EOBD_PROTOCOL_ISO9141:
                        -> VIDEO MESSAGE    "PROTOCOL ISO9141 NOT SUPPORTED"
                        break;

                    case EOBD_PROTOCOL_KW2000:
                        -> VIDEO MESSAGE    "PROTOCOL KW2000 NOT SUPPORTED"
                        break;

                    case EOBD_PROTOCOL_J1850PWM:
                        -> VIDEO MESSAGE    "PROTOCOL J1850PWM NOT SUPPORTED"
                        break;

                    case EOBD_PROTOCOL_J1850VPW:
                        -> VIDEO MESSAGE    "PROTOCOL J1850VPW NOT SUPPORTED"
                        break;
                }
            }
        }

        // RESET ENGINE WITHIN THE SCANTOOL
        // EOBD_FlashResetNoWait();
    }
}

EXIT:

// CLOSE EOBD SERIAL PORT
EOBD_Close();

// UNLOAD EOBD DLL RESOURCES
EOBD_UnloadDLL();
