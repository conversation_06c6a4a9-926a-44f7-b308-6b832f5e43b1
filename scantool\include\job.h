#pragma once

#define BUFLEN 8192
#define internal static

typedef enum JobStatus
{
	J_STOPPED,
	J_RUNNING
} JobStatus;

typedef struct AppSettings
{
	int scannerCom;
	int scannerScroll;
	int httpPortStatus;
	int usernameStatus;
	int passwordStatus;
	char httpPort[8];
	char username[64];
	char password[64];
} AppSettings;

typedef struct AppData
{
	AppSettings* settings;
    JobStatus status;
} AppData;

typedef enum AppStatus
{
	SCREEN_SETTINGS,
	SCREEN_MAIN
} AppStatus;

void StartScanner(AppData* data);
int InitScanner(void);