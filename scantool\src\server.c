#include <winsock2.h>
#include <stdlib.h>
#include <stdio.h>
#include <string.h>

#pragma comment(lib, "Ws2_32.lib")

#include "job.h"

#define REQUEST_SIZE 4096
#define DEFAULT_PORT 80

internal char* DEFAULT_ERROR_404 = "HTTP/1.1 404 Not Found\r\nContent-Type: text/html\r\n\r\n<!DOCTYPE html><html><head><title>404 Not Found</title></head><body><h1>Not Found</h1>The requested URL was not found on this server.</body></html>";
internal char* DEFAULT_START_OK = "HTTP/1.1 200 OK\r\nContent-Type: text/json\r\n\r\n{\"code\": 200}";
internal char* DEFAULT_STOP_OK = "HTTP/1.1 200 OK\r\nContent-Type: text/json\r\n\r\n{\"code\": 200, \"rpm\": %d, \"sound\": %.1f}";
internal char* DEFAULT_START_NOK = "HTTP/1.1 500 OK\r\nContent-Type: text/json\r\n\r\n{\"code\": 500}";
internal char* DEFAULT_NAUTH = "HTTP/1.1 400 OK\r\nContent-Type: text/json\r\n\r\n{\"code\": 400}";
internal char* DEFAULT_BACKGROUNDNOISE_OK = "HTTP/1.1 200 OK\r\nContent-Type: text/json\r\n\r\n{\"code\": 200, \"sound\": %.1f}";

internal int serverRunning;
internal void* serverThreadHandle;

internal const unsigned char base64_table[65] = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";

size_t base64Encode(const unsigned char *src, size_t len, const unsigned char *outStr, size_t dstLen)
{
    size_t olen = 4 * ((len + 2) / 3); /* 3-byte blocks to 4-byte */
    if (olen < len || olen > dstLen)
    {
        return 0;
    }

    const unsigned char* in = src;
    const unsigned char* end = src + len;
    const unsigned char* out = &outStr[0];
    unsigned char* pos = (unsigned char* ) out;

    while (end - in >= 3)
    {
        *pos++ = base64_table[in[0] >> 2];
        *pos++ = base64_table[((in[0] & 0x03) << 4) | (in[1] >> 4)];
        *pos++ = base64_table[((in[1] & 0x0f) << 2) | (in[2] >> 6)];
        *pos++ = base64_table[in[2] & 0x3f];
        in += 3;
    }

    if (end - in)
    {
        *pos++ = base64_table[in[0] >> 2];
        if (end - in == 1)
        {
            *pos++ = base64_table[(in[0] & 0x03) << 4];
            *pos++ = '=';
        }
        else
        {
            *pos++ = base64_table[((in[0] & 0x03) << 4) | (in[1] >> 4)];
            *pos++ = base64_table[(in[1] & 0x0f) << 2];
        }
        *pos++ = '=';
    }

    return olen;
}

internal int serverThread(void* lpParam)
{
    AppData* data = (AppData*) lpParam;
    serverRunning = 1;
    
    char authorization[1024];
    int authorizationLen = snprintf(authorization, 1024, "%s:%s", data->settings->username, data->settings->password);

    char basicAuth[1024] = {0};
    size_t basicAuthLen = base64Encode(authorization, authorizationLen, (const unsigned char*) & basicAuth, 1024);
    
    char* sport = data->settings->httpPort;
    int port = 0;

    while (*sport != 0)
    {
        port = (10 * port) + (*sport - '0');
        sport++;
    }

    if (port == 0)
    {
        port = DEFAULT_PORT;
    }

    WSADATA wsaData;
    if (WSAStartup(MAKEWORD(2, 2), &wsaData) == SOCKET_ERROR)
    {
        serverRunning = -1;
        return 0;
    }

    struct sockaddr_in local;
    local.sin_family = AF_INET;
    local.sin_addr.s_addr = INADDR_ANY;
    local.sin_port = htons(port);

    SOCKET sock = socket(AF_INET, SOCK_STREAM, 0);
    if (sock == INVALID_SOCKET)
    {
        WSACleanup();
        serverRunning = -1;
        return 0;
    }

    if (bind(sock, (struct sockaddr *)&local, sizeof(local)) == SOCKET_ERROR)
    {
        WSACleanup();
        serverRunning = -1;
        return 0;
    }

listen_goto:
    if (listen(sock, 10) == SOCKET_ERROR)
    {
        WSACleanup();
        serverRunning = -1;
        return 0;
    }

    int count = 0;

    while (serverRunning == 1)
    {
        struct sockaddr_in clientAddr;
        int addrLen = sizeof(clientAddr);
        SOCKET messageSocket = accept(sock, (struct sockaddr*)&clientAddr, &addrLen);

        if (messageSocket == INVALID_SOCKET || messageSocket == -1)
        {
            goto listen_goto;
        }

        int messageLength;
        char buf[REQUEST_SIZE];

        messageLength = recv(messageSocket, buf, sizeof(buf), 0);
        if (messageLength <= 0)
        {
            closesocket(messageSocket);
            continue;
        }

        char* authStr = strstr(buf, "Authorization: Basic ");
        if (authStr == 0)
        {
            send(messageSocket, DEFAULT_NAUTH, (int) strlen(DEFAULT_NAUTH), 0);
            closesocket(messageSocket);
            continue;
        }

        authStr = authStr + 21;
        int i = 0;
        int sucess = 0;

        if (strncmp(authStr, basicAuth, basicAuthLen) != 0 || (authStr[basicAuthLen] != '\r' && authStr[basicAuthLen] != '\n'))
        {
            send(messageSocket, DEFAULT_NAUTH, (int) strlen(DEFAULT_NAUTH), 0);
            closesocket(messageSocket);
            continue;
        }

        /*
        if (strncmp(buf, "GET /start ", 11) == 0)
        {
            if (RpmThreadIsRunning() || SoundThreadIsRunning())
            {
                send(messageSocket, DEFAULT_START_NOK, (int) strlen(DEFAULT_START_NOK), 0);
            }
            else
            {
                StartRpmThread(data);
                StartSoundThread(data);

                if (RpmThreadIsRunning() && SoundThreadIsRunning())
                {
                    send(messageSocket, DEFAULT_START_OK, (int) strlen(DEFAULT_START_OK), 0);
                }
                else
                {
                    send(messageSocket, DEFAULT_START_NOK, (int) strlen(DEFAULT_START_NOK), 0);
                }
            }
        }
        else if (strncmp(buf, "GET /stop ", 10) == 0)
        {
            int rpmValue = 0;
            if (RpmThreadIsRunning())
            {
                rpmValue = RpmGetMax();
                StopRpmThread();
            }

            float soundValue = 0.0f;
            if (SoundThreadIsRunning())
            {
                soundValue = SoundGetMaxValue();
                StopSoundThread();
            }

            char response[REQUEST_SIZE];
            int len = snprintf(response, REQUEST_SIZE, DEFAULT_STOP_OK, rpmValue, soundValue);
            send(messageSocket, response, len, 0);
        }
        else if (strncmp(buf, "GET /sync ", 9) == 0)
        {
            int rpmValue = 0;
            if (RpmThreadIsRunning())
            {
                rpmValue = RpmGetMax();
            }

            float soundValue = 0.0f;
            if (SoundThreadIsRunning())
            {
                soundValue = SoundGetMaxValue();
            }

            char response[REQUEST_SIZE];
            int len = snprintf(response, REQUEST_SIZE, DEFAULT_STOP_OK, rpmValue, soundValue);
            send(messageSocket, response, len, 0);
        }
        else if (strncmp(buf, "GET /backgroundnoise", 20) == 0)
        {
            float soundValue = 0.0f;
            StartSoundThread(data);
            if (!SoundThreadIsRunning())
            {
                send(messageSocket, DEFAULT_START_NOK, (int) strlen(DEFAULT_START_NOK), 0);
            }

            Sleep(2000);
            soundValue = SoundGetMaxValue();
            StopSoundThread();

            char response[REQUEST_SIZE];
            int len = snprintf(response, REQUEST_SIZE, DEFAULT_BACKGROUNDNOISE_OK, soundValue);
            send(messageSocket, response, len, 0);
        }        
        else
        {
            send(messageSocket, DEFAULT_ERROR_404, (int) strlen(DEFAULT_ERROR_404), 0);
        }
        */
       
        closesocket(messageSocket);
    }

    serverRunning = 1;
    return 0;
}

void StartServerThread(AppData* data)
{
    unsigned long threadId;
    serverThreadHandle = CreateThread(0, 0, serverThread, (void*)data, 0, &threadId);
    serverRunning = serverThreadHandle != 0 ? 1 : -1;
}

void StopServerThread(void)
{
    serverRunning = 0;
    CloseHandle(serverThreadHandle);
}

int ServerThreadIsRunning(void)
{
    return serverRunning == 1;
}

int ServerThreadIsError(void)
{
    return serverRunning == -1;
}
