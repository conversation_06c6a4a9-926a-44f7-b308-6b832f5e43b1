#pragma once

#define MIL_ON        1
#define MIL_OFF       2
#define MIL_BLINK     3
#define MIL_RESERVED  4

typedef enum {
    EOBD_PID_RPMOLD,                            //  0
    EOBD_PID_MIL_STATUS_01,                     //  1
    EOBD_PID_TEMP_05,                           //  2
    EOBD_PID_RPM_0C,                            //  3
    EOBD_PID_OXYGEN_13,                         //  4
    EOBD_PID_SPROBE_14,                         //  5
    EOBD_PID_SPROBE_16,                         //  6
    EOBD_PID_SPROBE_18,                         //  7
    EOBD_PID_SPROBE_1A,                         //  8
    EOBD_PID_EOBD_STATUS_1C,                    //  9
    EOBD_PID_OXYGEN_1D,                         // 10
    EOBD_PID_KM_MILON_21,                       // 11
    EOBD_PID_BPROBE_24,                         // 12
    EOBD_PID_BPROBE_26,                         // 13
    EOBD_PID_BPROBE_28,                         // 14
    EOBD_PID_BPROBE_2A,                         // 15
    EOBD_PID_BPROBE_34,                         // 16
    EOBD_PID_BPROBE_36,                         // 17
    EOBD_PID_BPROBE_38,                         // 18
    EOBD_PID_BPROBE_3A,                         // 19
    EOBD_PID_PED_ACCEL_49,                      // 20
    EOBD_PID_TIME_MILON_4D,                     // 21
    EOBD_PID_TEMP_67,                           // 22
    EOBD_PID_TEMP_6701,                         // 23
    EOBD_PID_AVG_AGENT_85,                      // 24
    EOBD_PID_RDNSUPP_FE,                        // 25
    EOBD_PID_RDNCMPL_FF,                        // 26
    EOBD_PID_DIAGNOSI_INFO,                     // 27
    EOBD_PID_CVN,                               // 28
    EOBD_PID_CALID,                             // 29
    EOBD_PID_VIN,                               // 30
    EOBD_PID_ECUADDRESS,                        // 31
    EOBD_PID_TOTAL_DISTANCE_TRAVELED_1701,      // 32
    EOBD_PID_TOTAL_FUEL_CONSUMED_1702,          // 33
    EOBD_PID_TOTAL_DISTANCE_ENGINE_OFF_1A01,    // 34
    EOBD_PID_TOTAL_DISTANCE_ENGINE_RUN_1A02,    // 35
    EOBD_PID_TOTAL_DISTANCE_SELECTABLE_1A03,    // 36
    EOBD_PID_TOTAL_FUEL_CHARGE_DEPLETING_1B01,  // 37
    EOBD_PID_TOTAL_FUEL_SELECTABLE_CHARGE_1B02, // 38
    EOBD_PID_TOTAL_GRID_ENERGY_BATTERY_1C01,    // 39
    EOBD_TOTAL_PID                              // 40 TOTAL
} EOBD_PID_E;

typedef enum {
    EOBD_SERIAL_VOID                = -1,
    EOBD_SERIAL_OK                  = 0,
    EOBD_SERIAL_ERROR               = 1,
    EOBD_SERIAL_BAD_PARAMETER       = 2,
    EOBD_SERIAL_MISSING_PAYMENT     = 3,
    EOBD_SERIAL_MISSING_ENGINEFILE  = 4
} EOBD_SERIAL_E;

typedef enum {
    EOBD_DEVICE_NONE            = 0,
    EOBD_DEVICE_FASTBOX,
    EOBD_DEVICE_ST6000,
    EOBD_DEVICE_BTOUCH,
    EOBD_DEVICE_FTOUCH,
    EOBD_DEVICE_BTP1000,
    EOBD_DEVICE_CONNEX,
    EOBD_DEVICE_MGT300EVO
} EOBD_DEVICE_E;

typedef enum {
    EOBD_MODULE_NONE            = 0x00,
    EOBD_MODULE_ASIA            = 0x01,
    EOBD_MODULE_MULTIPLEXER1    = 0x02,
    EOBD_MODULE_MULTIPLEXER2    = 0x03
} EOBD_MODULE_E;

typedef enum {
    EOBD_PROTOCOL_ISO27145,
    EOBD_PROTOCOL_J1939,
    EOBD_PROTOCOL_CANISO15765,
    EOBD_PROTOCOL_ISO9141,
    EOBD_PROTOCOL_KW2000,
    EOBD_PROTOCOL_J1850PWM,
    EOBD_PROTOCOL_J1850VPW,
    EOBD_PROTOCOL_ALLCAR,
    EOBD_PROTOCOL_ELECTRICAL_CONTINUITY
} EOBD_PROTOCOL_E;

typedef enum {
    EOBD_TEMPPID_AUTODETECT,
    EOBD_TEMPPID_0500,
    EOBD_TEMPPID_6700,
    EOBD_TEMPPID_6701,
    EOBD_TEMPPID_UNDEF
} EOBD_TEMPPID_E;

typedef struct {
    char            szVersionSoftware[32];
    char            szSerialNumber[32];
    char            szDevice[32];
    char            szDateTime[32];
    unsigned char   DeviceType;
} EOBD_FLASHINFO_S;

typedef struct {
    char            szDate[32];
    char            szHour[32];
    char            szVersion[32];
} EOBD_WHOINFO_S;

internal HANDLE jobThreadHandle;
internal char buffer[BUFLEN];
internal HINSTANCE handle;

typedef int (__stdcall* fptr_int_intptr)(int*);
typedef int (__stdcall* fptr_int_int)(int);
typedef void (__stdcall* fptr_void_str)(char*);
typedef int (__stdcall* fptr_int_str_int)(char*, int);
typedef void (__stdcall* fptr_void_void)(void);
typedef int (__stdcall* fptr_int_void)(void);
typedef void (__stdcall* fptr_void_int)(int);
typedef int (__stdcall* fptr_int_info)(EOBD_FLASHINFO_S*);
typedef int (__stdcall* fptr_int_int_int)(int, int);
typedef void (__stdcall* fptr_void_int_ul)(int, unsigned long);
typedef int (__stdcall* fptr_int_intptr_str_protocol)(int*, char*, EOBD_PROTOCOL_E);
typedef int (__stdcall* fptr_int_intptr_int)(int*, int);

internal fptr_int_int scanner_loadDLL;
internal fptr_void_str scanner_versionDll;
internal fptr_int_str_int scanner_engineVersion;
internal fptr_void_void scanner_unloadDll;
internal fptr_int_void scanner_open;
internal fptr_void_void scanner_close;
internal fptr_void_int scanner_enableLog;
internal fptr_void_int scanner_enableLogEcu;
internal fptr_int_void scanner_flashResetNoWait;
internal fptr_int_info scanner_flashInfo;
internal fptr_int_int_int scanner_testProtocol;
internal fptr_void_str scanner_getProtocol;
internal fptr_int_intptr scanner_statusMil;
internal fptr_void_int_ul scanner_activeIncDelay;
internal fptr_int_str_int scanner_readParameter;
internal fptr_void_str scanner_readVIN;
internal fptr_void_str scanner_readCALID;
internal fptr_int_intptr_str_protocol scanner_getErrors;
internal fptr_int_intptr_int scanner_fastRpm;
internal fptr_int_intptr_int scanner_fastTemp;
internal fptr_int_intptr scanner_fastSupportFast;
internal fptr_int_void scanner_stopPeriodicRpmTemp;
internal fptr_int_void scanner_restartPeriodicRpmTemp;
